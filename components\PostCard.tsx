import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Post } from '../types';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';
import HeartIcon from './icons/HeartIcon';
import ChatBubbleIcon from './icons/ChatBubbleIcon';
import ShareIcon from './icons/ShareIcon';
import TrashIcon from './icons/TrashIcon';
import PencilIcon from './icons/PencilIcon';

interface PostCardProps {
  post: Post;
  isCompact?: boolean;
}

const PostCard: React.FC<PostCardProps> = ({ post, isCompact = false }) => {
  const { currentUser, isAdmin } = useAuth();
  const { deletePost, toggleLikePost, addCommentToPost, deleteComment } = usePosts();
  const navigate = useNavigate();
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [isExpanded, setIsExpanded] = useState(!isCompact);

  // Determine display name and avatar for the post
  const displayUsername = post.isAnonymous && !isAdmin ? 'Anonymous' : post.username;
  const displayAvatarUrl = post.isAnonymous && !isAdmin ? 'https://picsum.photos/seed/anonymous/100/100' : post.userAvatarUrl;

  const timeSince = (dateString: string): string => {
    const date = new Date(dateString);
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
    let interval = seconds / 31536000;
    if (interval > 1) return Math.floor(interval) + "y";
    interval = seconds / 2592000;
    if (interval > 1) return Math.floor(interval) + "mo";
    interval = seconds / 86400;
    if (interval > 1) return Math.floor(interval) + "d";
    interval = seconds / 3600;
    if (interval > 1) return Math.floor(interval) + "h";
    interval = seconds / 60;
    if (interval > 1) return Math.floor(interval) + "m";
    return Math.floor(seconds) + "s";
  };

  const handleCommentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim() && currentUser) {
      try {
        await addCommentToPost(post.id, newComment.trim(), currentUser);
        setNewComment('');
      } catch (error) {
        console.error('Error submitting comment:', error);
        alert('Failed to add comment. Please try again.');
      }
    }
  };

  const canEditOrDelete = isAdmin || currentUser?.id === post.userId || (post.isAnonymous && currentUser?.id === post.actualUserId);

  const handleCardClick = () => {
    if (isCompact) {
      setIsExpanded(!isExpanded);
    }
  };

  const handleUserClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Only navigate if it's not an anonymous post, or if admin is viewing anonymous post
    if (!post.isAnonymous || isAdmin) {
      const userId = post.isAnonymous ? post.actualUserId : post.userId;
      if (userId) {
        navigate(`/user/${userId}`);
      }
    }
  };

  return (
    <article
      className={`bg-neutral-surface rounded-lg shadow-xl overflow-hidden border border-neutral-border animate-slide-up hover-glow transition-all duration-300 cyber-border ${
        isCompact ? 'my-2 cursor-pointer hover:scale-[1.01]' : 'my-4 hover:scale-[1.02]'
      }`}
      onClick={handleCardClick}
    >
      <header className={`${isCompact ? 'p-3' : 'p-4'} flex items-center justify-between`}>
        <div className="flex items-center flex-1">
          <button
            className={`${isCompact ? 'w-8 h-8' : 'w-10 h-10'} rounded-full mr-3 border-2 border-brand-primary hover-scale transition-transform duration-200 overflow-hidden ${
              (!post.isAnonymous || isAdmin) ? 'cursor-pointer hover:border-brand-secondary' : 'cursor-default'
            }`}
            onClick={handleUserClick}
            disabled={post.isAnonymous && !isAdmin}
            title={(!post.isAnonymous || isAdmin) ? `View ${displayUsername}'s profile` : 'Anonymous user'}
          >
            <img
              src={displayAvatarUrl}
              alt={displayUsername}
              className="w-full h-full object-cover"
            />
          </button>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              {/* Username - clickable for non-anonymous posts or admin viewing anonymous */}
              {!post.isAnonymous || isAdmin ? (
                <button
                  className={`font-semibold text-neutral-100 hover:text-brand-primary transition-colors duration-200 ${isCompact ? 'text-sm' : ''}`}
                  onClick={handleUserClick}
                  title={`View ${displayUsername}'s profile`}
                >
                  {displayUsername}
                </button>
              ) : (
                <p className={`font-semibold text-neutral-100 ${isCompact ? 'text-sm' : ''}`}>{displayUsername}</p>
              )}

              {post.isAnonymous && isAdmin && (
                <span className="text-xs bg-accent-warning/20 text-yellow-400 px-2 py-0.5 rounded-full">
                  Actually: {post.username}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <p className="text-xs text-neutral-muted">{timeSince(post.timestamp)} ago</p>
              {isCompact && !isExpanded && (
                <span className="text-xs text-brand-primary animate-pulse">Click to expand</span>
              )}
            </div>
          </div>
        </div>
        {canEditOrDelete && (
          <div className="flex items-center space-x-1" onClick={(e) => e.stopPropagation()}>
            <Link
              to={`/edit-post/${post.id}`}
              className="text-neutral-muted hover:text-brand-primary p-1 rounded-md transition-all duration-200 hover-scale hover-text-glow"
              title="Edit Post"
            >
              <PencilIcon className={`${isCompact ? 'w-4 h-4' : 'w-5 h-5'}`} />
            </Link>
            <button
              onClick={(e) => {
                e.stopPropagation();
                deletePost(post.id);
              }}
              className="text-accent-error hover:text-red-400 p-1 rounded-md transition-all duration-200 hover-scale animate-cyber-flicker"
              title="Delete Post"
            >
              <TrashIcon className={`${isCompact ? 'w-4 h-4' : 'w-5 h-5'}`} />
            </button>
          </div>
        )}
      </header>

      {/* Show content based on compact mode and expansion state */}
      {isCompact && !isExpanded ? (
        // Compact view - show image (if exists) and title/caption
        <>
          {post.imageUrl && (
            <div className="compact-image-hover">
              <img
                src={post.imageUrl}
                alt={`Post by ${post.username}: ${post.caption}`}
                className="w-full h-48 object-cover transition-all duration-200 hover:brightness-110"
                onError={(e) => e.currentTarget.style.display='none'} // Hide if image fails to load
              />
            </div>
          )}
          <div className={`${isCompact ? (post.imageUrl ? 'px-3 pb-2 pt-3' : 'px-3 pb-2 pt-1') : 'p-4'}`}>
            {post.caption && (
              <p className="text-neutral-200 text-sm font-medium line-clamp-2 whitespace-pre-wrap">{post.caption}</p>
            )}
            {!post.caption && post.contentBody && (
              <p className="text-neutral-100 text-sm line-clamp-2 whitespace-pre-wrap">{post.contentBody}</p>
            )}
          </div>
        </>
      ) : (
        // Full view - show everything
        <>
          {post.imageUrl && (
            <img
              src={post.imageUrl}
              alt={`Post by ${post.username}: ${post.caption}`}
              className="w-full h-auto object-cover max-h-[70vh]"
              onError={(e) => e.currentTarget.style.display='none'} // Hide if image fails to load
            />
          )}

          <div className={`${isCompact ? 'p-3' : 'p-4'}`}>
            {post.caption && (
                <p className="text-neutral-200 text-sm italic mb-2 whitespace-pre-wrap">{post.caption}</p>
            )}
            {post.contentBody && (
               <p className="text-neutral-100 my-3 whitespace-pre-wrap">{post.contentBody}</p>
            )}

            {post.tags && post.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {post.tags.map(tag => (
                  <span key={tag} className="text-xs bg-neutral-base text-brand-primary px-2 py-0.5 rounded-full">
                    #{tag}
                  </span>
                ))}
              </div>
            )}

            <div className="flex items-center space-x-4 text-neutral-muted mb-3 pt-3 border-t border-neutral-border" onClick={(e) => e.stopPropagation()}>
              <button
                onClick={async (e) => {
                  e.stopPropagation();
                  if (currentUser) {
                    try {
                      await toggleLikePost(post.id, currentUser.id);
                    } catch (error) {
                      console.error('Error toggling like:', error);
                    }
                  }
                }}
                className={`flex items-center space-x-1 hover:text-red-500 transition-all duration-200 hover-scale hover-text-glow ${post.isLikedByCurrentUser ? 'text-red-500 animate-pulse-glow' : ''}`}
                title={post.isLikedByCurrentUser ? "Unlike" : "Like"}
                disabled={!currentUser}
              >
                <HeartIcon className={`${isCompact ? 'w-5 h-5' : 'w-6 h-6'}`} isFilled={post.isLikedByCurrentUser} /> <span>{post.likes}</span>
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowComments(!showComments);
                }}
                className="flex items-center space-x-1 hover:text-brand-primary transition-all duration-200 hover-scale hover-text-glow"
                title="Comments"
              >
                <ChatBubbleIcon className={`${isCompact ? 'w-5 h-5' : 'w-6 h-6'}`} /> <span>{post.comments.length}</span>
              </button>
              <button
                onClick={(e) => e.stopPropagation()}
                className="flex items-center space-x-1 hover:text-brand-primary transition-all duration-200 hover-scale hover-text-glow"
                title="Share (Placeholder)"
              >
                <ShareIcon className={`${isCompact ? 'w-5 h-5' : 'w-6 h-6'}`} />
              </button>
            </div>

            {showComments && (
              <div className="mt-4 space-y-3 pt-3 border-t border-neutral-border" onClick={(e) => e.stopPropagation()}>
                {post.comments.length === 0 && <p className="text-sm text-neutral-muted">No comments yet.</p>}
                {post.comments.map(comment => (
                  <div key={comment.id} className="flex items-start space-x-2 text-sm">
                    <button
                      className="w-6 h-6 rounded-full mt-0.5 border border-neutral-border hover-scale transition-transform duration-200 overflow-hidden cursor-pointer hover:border-brand-primary"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/user/${comment.userId}`);
                      }}
                      title={`View ${comment.username}'s profile`}
                    >
                      <img src={comment.avatarUrl} alt={comment.username} className="w-full h-full object-cover" />
                    </button>
                    <div className="flex-grow bg-neutral-base p-2 rounded-md">
                      <div className="flex justify-between items-center">
                        <button
                          className="font-semibold text-neutral-100 hover:text-brand-primary transition-colors duration-200 text-left"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/user/${comment.userId}`);
                          }}
                          title={`View ${comment.username}'s profile`}
                        >
                          {comment.username}
                        </button>
                        <span className="text-xs text-neutral-muted">{timeSince(comment.timestamp)}</span>
                      </div>
                      <p className="text-neutral-200 mt-0.5 whitespace-pre-wrap">{comment.text}</p>
                    </div>
                     {(isAdmin || currentUser?.id === comment.userId) && (
                      <button
                        onClick={async (e) => {
                          e.stopPropagation();
                          try {
                            await deleteComment(post.id, comment.id);
                          } catch (error) {
                            console.error('Error deleting comment:', error);
                            alert('Failed to delete comment. Please try again.');
                          }
                        }}
                        className="text-accent-error hover:text-red-400 p-1 rounded-md transition-colors self-center ml-1"
                        title="Delete Comment"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                ))}
                {currentUser && (
                  <form onSubmit={handleCommentSubmit} className="flex items-center space-x-2 mt-3">
                    <img src={currentUser.avatarUrl} alt={currentUser.username} className="w-8 h-8 rounded-full" />
                    <input
                      type="text"
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder="Add a comment..."
                      className="flex-grow bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-1 focus:ring-brand-primary focus:border-brand-primary text-sm"
                    />
                    <button type="submit" className="bg-brand-primary hover:bg-brand-secondary text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">Post</button>
                  </form>
                )}
                {!currentUser && <p className="text-sm text-neutral-muted mt-2">Please log in to comment.</p>}
              </div>
            )}
          </div>
        </>
      )}

    </article>
  );
};

export default PostCard;