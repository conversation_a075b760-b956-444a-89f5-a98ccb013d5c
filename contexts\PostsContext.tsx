import React, { createContext, useState, ReactNode, useCallback, useMemo, useEffect, useContext } from 'react';
import { Post, Comment, User } from '../types';
import { v4 as uuidv4 } from 'uuid';
import { AuthContext } from './AuthContext';
import {
  getAllPosts,
  createPost as firebaseCreatePost,
  updatePost as firebaseUpdatePost,
  deletePost as firebaseDeletePost,
  getPostById,
  likePost,
  unlikePost
} from '../services/firebaseService';


type EditablePostData = Partial<Pick<Post, 'imageUrl' | 'caption' | 'contentBody' | 'tags'>>;

interface PostsContextType {
  posts: Post[];
  loading: boolean;
  getPostById: (postId: string) => Post | undefined;
  addPost: (postData: Omit<Post, 'id' | 'likes' | 'comments' | 'timestamp' | 'isLikedByCurrentUser'>) => Promise<void>;
  editPost: (postId: string, updatedData: EditablePostData) => Promise<void>;
  deletePost: (postId: string) => Promise<void>;
  toggleLikePost: (postId: string, userId: string) => Promise<void>;
  addCommentToPost: (postId: string, commentText: string, currentUser: User) => void;
  deleteComment: (postId: string, commentId: string) => void;
  refreshPosts: () => Promise<void>;
  updatePostsWithCurrentUser: (currentUserId: string | null) => void;
}

export const PostsContext = createContext<PostsContextType | undefined>(undefined);

interface PostsProviderProps {
  children: ReactNode;
}

export const PostsProvider: React.FC<PostsProviderProps> = ({ children }) => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const authContext = useContext(AuthContext);

  // Get current user from auth context
  const currentUser = authContext?.currentUser;
  const authLoading = authContext?.loading;

  const refreshPosts = useCallback(async () => {
    try {
      setLoading(true);

      // Check if user is authenticated before trying to load posts
      if (!currentUser) {
        console.log('PostsContext: No current user, clearing posts');
        setPosts([]);
        return;
      }

      console.log('PostsContext: Loading posts for user:', currentUser.username);
      const fetchedPosts = await getAllPosts();
      console.log('PostsContext: Fetched', fetchedPosts.length, 'posts');
      // Note: isLikedByCurrentUser will be set by PostsUpdater component
      setPosts(fetchedPosts);
    } catch (error) {
      console.error('Error loading posts:', error);
      // If there's a permission error, clear posts
      setPosts([]);
    } finally {
      setLoading(false);
    }
  }, [currentUser]);

  // Load posts when user authentication state changes
  useEffect(() => {
    console.log('PostsContext useEffect triggered:', {
      currentUser: currentUser?.username || 'none',
      authLoading,
      hasAuthContext: !!authContext
    });

    // Don't load posts if auth is still loading
    if (authLoading) {
      console.log('PostsContext: Auth still loading, skipping posts fetch');
      return;
    }

    refreshPosts();
  }, [currentUser, authLoading, refreshPosts]);

  const getPostById = useCallback((postId: string) => {
    return posts.find(post => post.id === postId);
  }, [posts]);

  const addPost = useCallback(async (postData: Omit<Post, 'id' | 'likes' | 'comments' | 'timestamp' | 'isLikedByCurrentUser'>) => {
    try {
      await firebaseCreatePost(postData);
      await refreshPosts(); // Refresh to get the new post
    } catch (error) {
      console.error('Error creating post:', error);
      throw error;
    }
  }, [refreshPosts]);

  const editPost = useCallback(async (postId: string, updatedData: EditablePostData) => {
    try {
      await firebaseUpdatePost(postId, updatedData);
      await refreshPosts(); // Refresh to get the updated post
    } catch (error) {
      console.error('Error updating post:', error);
      throw error;
    }
  }, [refreshPosts]);

  const deletePost = useCallback(async (postId: string) => {
    try {
      await firebaseDeletePost(postId);
      setPosts(prevPosts => prevPosts.filter(post => post.id !== postId));
    } catch (error) {
      console.error('Error deleting post:', error);
      throw error;
    }
  }, []);

  const toggleLikePost = useCallback(async (postId: string, userId: string) => {
    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      const isLiked = post.likedUsers?.includes(userId) || false;

      if (isLiked) {
        await unlikePost(postId, userId);
      } else {
        await likePost(postId, userId);
      }

      // Update local state optimistically
      setPosts(prevPosts =>
        prevPosts.map(p =>
          p.id === postId
            ? {
                ...p,
                likes: isLiked ? Math.max((p.likes || 0) - 1, 0) : (p.likes || 0) + 1,
                likedUsers: isLiked
                  ? (p.likedUsers || []).filter(id => id !== userId)
                  : [...(p.likedUsers || []), userId],
                isLikedByCurrentUser: !isLiked,
              }
            : p
        )
      );
    } catch (error) {
      console.error('Error toggling like:', error);
      throw error;
    }
  }, [posts]);

  const addCommentToPost = useCallback((postId: string, commentText: string, currentUser: User) => {
    const newComment: Comment = {
      id: uuidv4(),
      userId: currentUser.id,
      username: currentUser.username,
      avatarUrl: currentUser.avatarUrl,
      text: commentText,
      timestamp: new Date().toISOString(),
    };
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, comments: [newComment, ...post.comments] } // Add new comments to the beginning
          : post
      )
    );
  }, []);

  const deleteComment = useCallback((postId: string, commentId: string) => {
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, comments: post.comments.filter(comment => comment.id !== commentId) }
          : post
      )
    );
  }, []);

  const updatePostsWithCurrentUser = useCallback((currentUserId: string | null) => {
    setPosts(prevPosts =>
      prevPosts.map(post => ({
        ...post,
        isLikedByCurrentUser: currentUserId ? (post.likedUsers || []).includes(currentUserId) : false
      }))
    );
  }, []);

  const contextValue = useMemo(() => ({
    posts,
    loading: loading || authLoading, // Show loading if either auth or posts are loading
    getPostById,
    addPost,
    editPost,
    deletePost,
    toggleLikePost,
    addCommentToPost,
    deleteComment,
    refreshPosts,
    updatePostsWithCurrentUser,
  }), [posts, loading, authLoading, getPostById, addPost, editPost, deletePost, toggleLikePost, addCommentToPost, deleteComment, refreshPosts, updatePostsWithCurrentUser]);


  return (
    <PostsContext.Provider value={contextValue}>
      {children}
    </PostsContext.Provider>
  );
};